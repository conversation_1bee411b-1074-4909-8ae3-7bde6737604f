import { Player } from '@remotion/player';
import { VideoStitching } from './remotion/VideoStitching';
import { useVideoManager } from './hooks/useVideoManager';
import { calculateTotalDurationInFrames } from './utils/videoUtils';
import './App.css'

function App() {
  // Use the custom hook for video management
  const {
    uploadedVideos,
    isProcessing,
    playerDuration,
    handleFileSelection,
    removeVideo
  } = useVideoManager();

  // Calculate total duration for the composition
  const totalDurationInFrames = calculateTotalDurationInFrames(uploadedVideos);

  return (
    <div style={{ padding: '20px', display: 'flex', gap: '20px', maxWidth: '1400px', margin: '0 auto' }}>
      {/* Left sidebar for video upload and list */}
      <div style={{
        width: '300px',
        backgroundColor: '#f5f5f5',
        borderRadius: '8px',
        padding: '20px',
        height: 'fit-content'
      }}>
        <h2 style={{ margin: '0 0 20px 0', fontSize: '18px' }}>Video Upload</h2>

        {/* File input */}
        <div style={{ marginBottom: '20px' }}>
          <input
            type="file"
            multiple
            accept="video/*"
            onChange={handleFileSelection}
            disabled={isProcessing}
            style={{
              width: '100%',
              padding: '10px',
              border: '2px dashed #ccc',
              borderRadius: '4px',
              backgroundColor: 'white',
              cursor: isProcessing ? 'not-allowed' : 'pointer'
            }}
          />
          {isProcessing && (
            <p style={{ fontSize: '12px', color: '#666', margin: '5px 0 0 0' }}>
              Processing videos...
            </p>
          )}
        </div>

        {/* Uploaded videos list */}
        <div>
          <h3 style={{ margin: '0 0 10px 0', fontSize: '16px' }}>
            Uploaded Videos ({uploadedVideos.length})
          </h3>

          {uploadedVideos.length === 0 ? (
            <p style={{ fontSize: '14px', color: '#666', fontStyle: 'italic' }}>
              No videos uploaded yet. Select video files to get started.
            </p>
          ) : (
            <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
              {uploadedVideos.map((video, index) => (
                <div key={video.id} style={{
                  backgroundColor: 'white',
                  border: '1px solid #ddd',
                  borderRadius: '4px',
                  padding: '10px',
                  marginBottom: '8px',
                  fontSize: '12px',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'flex-start'
                }}>
                  <div style={{ flex: 1 }}>
                    <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>
                      {index + 1}. {video.name}
                    </div>
                    <div style={{ color: '#666' }}>
                      Duration: {video.duration.toFixed(1)}s ({video.durationInFrames} frames)
                    </div>
                  </div>
                  <button
                    onClick={() => removeVideo(video.id)}
                    style={{
                      backgroundColor: '#ff4444',
                      color: 'white',
                      border: 'none',
                      borderRadius: '3px',
                      padding: '4px 8px',
                      fontSize: '10px',
                      cursor: 'pointer',
                      marginLeft: '8px'
                    }}
                    title="Remove video"
                  >
                    ✕
                  </button>
                </div>
              ))}
            </div>
          )}

          {uploadedVideos.length > 0 && (
            <div style={{
              marginTop: '15px',
              padding: '10px',
              backgroundColor: '#e8f4fd',
              borderRadius: '4px',
              fontSize: '12px'
            }}>
              <strong>Total Duration: {(totalDurationInFrames / 30).toFixed(1)}s</strong>
            </div>
          )}
        </div>
      </div>

      {/* Main content area */}
      <div style={{ flex: 1 }}>
        <h1>Remotion Video Editor</h1>
        <p>Phase 2: Video Upload & State Management</p>

        {/* Video player area */}
        <div style={{
          width: '100%',
          border: '2px solid #ddd',
          borderRadius: '8px',
          overflow: 'hidden',
          backgroundColor: '#f0f0f0',
          minHeight: '400px'
        }}>
          <div style={{ padding: '10px', backgroundColor: '#333', color: 'white' }}>
            Video Preview {uploadedVideos.length > 0 ? `(${uploadedVideos.length} clips)` : '(No videos uploaded)'}
          </div>
          <div style={{ padding: '20px' }}>
            {uploadedVideos.length === 0 ? (
              <div style={{
                textAlign: 'center',
                padding: '60px 20px',
                color: '#666'
              }}>
                <h3>No videos to preview</h3>
                <p>Upload video files using the sidebar to see them here.</p>
              </div>
            ) : (
              <Player
                component={VideoStitching}
                durationInFrames={playerDuration}
                compositionWidth={1920}
                compositionHeight={1080}
                fps={30}
                style={{
                  width: '100%',
                  height: '400px',
                  border: '1px solid #ccc'
                }}
                controls={true}
                loop={true}
                inputProps={{ videos: uploadedVideos }}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default App
