import { parseMedia } from '@remotion/media-parser';
import { UploadedVideo } from '../types/video';

/**
 * Extract video duration using parseMedia from @remotion/media-parser
 * @param file - The video file to analyze
 * @returns Promise<number> - Duration in seconds
 */
export const getVideoDuration = async (file: File): Promise<number> => {
  try {
    // Import parseMedia dynamically to avoid bundling issues
    const { parseMedia } = await import('@remotion/media-parser');

    const objectUrl = URL.createObjectURL(file);

    try {
      const { slowDurationInSeconds } = await parseMedia({
        src: objectUrl,
        fields: { slowDurationInSeconds: true },
      });

      URL.revokeObjectURL(objectUrl);

      if (slowDurationInSeconds && isFinite(slowDurationInSeconds)) {
        return slowDurationInSeconds;
      } else {
        throw new Error('Invalid duration returned from parseMedia');
      }
    } catch (parseError) {
      URL.revokeObjectURL(objectUrl);
      throw parseError;
    }
  } catch (error) {
    console.error('Error extracting video duration:', error);
    // Return a default duration instead of throwing
    return 1.0; // 1 second default
  }
};

/**
 * Calculate dynamic duration for the video composition using parseMedia for accurate metadata
 * @param uploadedVideos - Array of uploaded videos
 * @returns Promise<number> - Total duration in frames
 */
export const calculateDynamicDuration = async (uploadedVideos: UploadedVideo[]): Promise<number> => {
  if (uploadedVideos.length === 0) {
    return 30; // 1 second minimum for empty state
  }

  let totalDuration = 0;

  for (const video of uploadedVideos) {
    try {
      const { slowDurationInSeconds } = await parseMedia({
        src: video.src,
        fields: { slowDurationInSeconds: true },
      });

      if (slowDurationInSeconds && isFinite(slowDurationInSeconds)) {
        totalDuration += Math.floor(slowDurationInSeconds * 30);
      } else {
        // Fallback to the duration from the video object if parseMedia fails
        totalDuration += video.durationInFrames || 30;
      }
    } catch (error) {
      console.warn(`Failed to parse metadata for video ${video.name}:`, error);
      // Fallback to the duration from the video object
      totalDuration += video.durationInFrames || 30;
    }
  }

  return Math.max(totalDuration, 30); // Minimum 1 second
};

/**
 * Calculate total duration in frames for all uploaded videos
 * @param uploadedVideos - Array of uploaded videos
 * @returns number - Total duration in frames
 */
export const calculateTotalDurationInFrames = (uploadedVideos: UploadedVideo[]): number => {
  return uploadedVideos.reduce((total, video) => total + video.durationInFrames, 0);
};
